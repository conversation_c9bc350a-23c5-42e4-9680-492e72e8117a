"use client"
import React, { memo, useEffect, useRef } from 'react'
import ReactECharts, { EChartsInstance } from 'echarts-for-react';

interface HeatMapProps {
  xData: string[];
  yData: string[];
  data: (string | number)[][];
}

const HeatMap: React.FC<HeatMapProps> = memo(({ xData, yData, data }) => {
  let chartInstance = useRef<EChartsInstance>(null)

  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        // 获取当前选项
        const currentOptions = chartInstance.current.getEchartsInstance().getOption();
        // 更新标签显示状态
        currentOptions.series[0].label.show = chartInstance.current?.ele.clientWidth > 768;
        // 更新 visualMap 显示状态
        currentOptions.visualMap[0].show = chartInstance.current?.ele.clientWidth > 768;
        // 应用更新后的选项
        chartInstance.current.getEchartsInstance().setOption(currentOptions);
        // 调整大小
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);

    // 监听容器大小变化
    const resizeObserver = new ResizeObserver(handleResize);
    const chartContainer = chartInstance.current?.ele;
    if (chartContainer) {
      resizeObserver.observe(chartContainer);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartContainer) {
        resizeObserver.unobserve(chartContainer);
      }
      resizeObserver.disconnect();
    };
  }, []);

  const options = {
    tooltip: {
      position: 'bottom'
    },
    grid: {
      height: '80%',
      bottom: '15%',
      width: '90%',
      left: 'left',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xData,
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: yData,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: Math.min(...data.map(item => item[2] as number)),
      max: Math.max(...data.map(item => item[2] as number)),
      calculable: true,
      orient: 'vertical',
      right: '0%',
      top: 'center',
      bottom: 'auto',
      text: ['高', '低'],
      textStyle: {
        color: '#333'
      },
      itemWidth: 15,
      itemHeight: 100,
      inRange: {
        color: 
          ['#375093', '#4E70AF', '#7091C7', '#9EBCDB', '#E8EDF1', '#F2EBE5', '#ECD0B4', '#C16D58', '#b7705c', '#A13D3B', '#831A21']
      },
      textGap: 10,
      align: 'left',
      show: true
    },
    series: [
      {
        name: '详细信息',
        type: 'heatmap',
        data: data,
        label: {
          show: true,
          formatter: (params: any) => {
            return params.value[2].toString();
          }
        },
        itemStyle: {
          borderColor: '#333',
          borderWidth: 0.5,
        },
        tooltip: {
          formatter: (params: any) => {

            const value = params.value;
            const xLabel = xData[value[0]];
            const yLabel = yData[value[1]];
            const dataValue = value[2];
            
            return `
              <div class="flex flex-col">
                <div class="text-sm font-bold">详细信息</div>
                <div class="text-sm">区域：${yLabel}</div>
                <div class="text-sm">月份：${xLabel}</div>
                <div class="text-sm">数值：${dataValue}</div>
              </div>
            `;
          }
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(42, 21, 133, 0.5)'
          }
        }
      }
    ]
  }

  return (
    <div className='w-full h-full'>
      <ReactECharts ref={chartInstance} option={options} className='overflow-visible'/>
    </div>
  )
});

export default HeatMap;
