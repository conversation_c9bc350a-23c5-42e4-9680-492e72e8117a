"use client"
import EChartsReact, { EChartsInstance } from 'echarts-for-react';
import React, { useEffect } from 'react'

interface LineChartProps {
  data?: number[];
  xLabels?: string[];
}

const LineChart: React.FC<LineChartProps> = ({ data, xLabels }) => {
  const chartRef = React.useRef<EChartsInstance>(null);

  const option = {
    // 全局配置
    backgroundColor: 'transparent',
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut',

    // 网格配置
    grid: {
      top: '12%',
      left: '4%',
      right: '4%',
      bottom: '8%',
      containLabel: true
    },

    // 工具提示
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e2e8f0',
      borderWidth: 1,
      borderRadius: 8,
      textStyle: {
        color: '#374151',
        fontSize: 12
      },
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#9ca3af',
          width: 1,
          type: 'dashed'
        },
        lineStyle: {
          color: '#6366f1',
          width: 2,
          opacity: 0.6
        }
      },
      formatter: function(params: any) {
        const point = params[0];
        return `
          <div style="font-weight: 600; margin-bottom: 4px; color: #1f2937;">${point.name}</div>
          <div style="display: flex; align-items: center;">
            <span style="display: inline-block; width: 8px; height: 8px; background: ${point.color}; border-radius: 50%; margin-right: 6px;"></span>
            <span style="color: #374151;">数值: ${point.value}</span>
          </div>
        `;
      }
    },

    // X轴配置
    xAxis: {
      type: 'category',
      data: xLabels,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#6366f1',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 11,
        fontWeight: 500,
        margin: 8
      },
      splitLine: {
        show: false
      }
    },

    // Y轴配置
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
        lineStyle: {
          color: '#333',
          width: 1
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 11,
        fontWeight: 500,
        margin: 12,
        formatter: '{value}'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f3f4f6',
          width: 1,
          type: 'solid'
        }
      }
    },

    // 数据系列
    series: [
      {
        name: '数据趋势',
        data: data,
        type: 'line',
        smooth: true,
        smoothMonotone: 'x',
        symbol: 'circle',
        symbolSize: 6,

        // 线条样式
        lineStyle: {
          color: '#6366f1',
          width: 3,
          shadowColor: 'rgba(99, 102, 241, 0.3)',
          shadowBlur: 8,
          shadowOffsetY: 2
        },

        // 数据点样式
        itemStyle: {
          color: '#6366f1',
          borderColor: '#ffffff',
          borderWidth: 2,
          shadowColor: 'rgba(99, 102, 241, 0.4)',
          shadowBlur: 6,
          shadowOffsetY: 2
        },

        // 高亮状态
        emphasis: {
          itemStyle: {
            color: '#4f46e5',
            borderColor: '#ffffff',
            borderWidth: 3,
            shadowColor: 'rgba(79, 70, 229, 0.6)',
            shadowBlur: 10,
            shadowOffsetY: 3,
            scale: 1.2
          },
          lineStyle: {
            width: 4,
            shadowBlur: 12
          }
        },

        // 区域填充样式
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(99, 102, 241, 0.3)'
              },
              {
                offset: 0.5,
                color: 'rgba(99, 102, 241, 0.15)'
              },
              {
                offset: 1,
                color: 'rgba(99, 102, 241, 0.05)'
              }
            ]
          },
          shadowColor: 'rgba(99, 102, 241, 0.2)',
          shadowBlur: 10,
          shadowOffsetY: 2
        },

        // 动画配置
        animationDelay: function (idx: number) {
          return idx * 100;
        },
        animationEasing: 'elasticOut'
      }
    ]
  };

  // 自适应尺寸
    useEffect(() => {
      const handleResize = () => {
        if (chartRef.current) {
          chartRef.current.resize();
        }
      };
  
      handleResize();
  
      window.addEventListener('resize', handleResize);
  
      // 监听容器大小变化
      const resizeObserver = new ResizeObserver(handleResize);
      const chartContainer = chartRef.current?.ele;
      if (chartContainer) {
        resizeObserver.observe(chartContainer);
      }
  
      return () => {
        window.removeEventListener('resize', handleResize);
        if (chartContainer) {
          resizeObserver.unobserve(chartContainer);
        }
        resizeObserver.disconnect();
      };
    }, []);

  return (
    <div className='w-full h-full'>
      <EChartsReact ref={chartRef} option={option} className='overflow-hidden' style={{ height: 'calc(100% - 20px)', width: '100%' }}/>
    </div>
  )
}

export default LineChart;