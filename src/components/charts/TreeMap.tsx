"use client"
import React, { useEffect, useRef, useImperativeHandle, forwardRef, memo } from 'react'
import ReactECharts, { EChartsInstance } from 'echarts-for-react';

interface TreeNode {
  name: string;
  value?: number;
  children?: TreeNode[];
}

interface TreeMapProps {
  data: TreeNode;
  height?: string | number;
  symbolSize?: number;
  initialTreeDepth?: number;
  edgeShape?: 'curve' | 'polyline';
  edgeForkPosition?: string;
  expandAndCollapse?: boolean;
  animationDuration?: number;
  animationDurationUpdate?: number;
  nodeSpacing?: number;
  enableZoomControl?: boolean;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onResetZoom?: () => void;
  zoomLevel?: number;
}

export interface TreeMapRef {
  resetChart: () => void;
}

const TreeMap = memo(forwardRef<TreeMapRef, TreeMapProps>(({
  data,
  symbolSize = 7,
  initialTreeDepth = 3,
  edgeShape = 'polyline',
  edgeForkPosition = '63%',
  expandAndCollapse = true,
  animationDuration = 550,
  animationDurationUpdate = 750,
  nodeSpacing = 0.8,
  enableZoomControl: _enableZoomControl = false,
  onZoomIn: _onZoomIn,
  onZoomOut: _onZoomOut,
  onResetZoom: _onResetZoom,
  zoomLevel = 1
}, ref) => {
  const chartRef = useRef<EChartsInstance>(null);

  // 重置图表位置和缩放的方法
  const resetChart = () => {
    if (chartRef.current) {
      const chartInstance = chartRef.current.getEchartsInstance();
      // 重置图表的平移和缩放状态
      chartInstance.dispatchAction({
        type: 'restore'
      });
    }
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    resetChart
  }));

  // 当zoomLevel变化时更新图表
  useEffect(() => {
    if (chartRef.current) {
      const chartInstance = chartRef.current.getEchartsInstance();
      const currentOption = chartInstance.getOption();
      chartInstance.setOption({
        ...currentOption,
        series: [{
          ...currentOption.series[0],
          zoom: zoomLevel
        }]
      });
    }
  }, [zoomLevel]);

  // 自适应尺寸
  useEffect(() => {
    const handleResize = () => {
      if (chartRef.current) {
        chartRef.current.resize();
      }
    };

    handleResize();

    window.addEventListener('resize', handleResize);

    // 监听容器大小变化
    const resizeObserver = new ResizeObserver(handleResize);
    const chartContainer = chartRef.current?.ele;
    if (chartContainer) {
      resizeObserver.observe(chartContainer);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartContainer) {
        resizeObserver.unobserve(chartContainer);
      }
      resizeObserver.disconnect();
    };
  }, []);

  // 计算所有节点的value值，用于确定样式范围
  const getAllValues = (node: TreeNode): number[] => {
    const values: number[] = [];
    if (node.value !== undefined) {
      values.push(node.value);
    }
    if (node.children) {
      node.children.forEach(child => {
        values.push(...getAllValues(child));
      });
    }
    return values;
  };

  const allValues = getAllValues(data);
  const maxValue = allValues.length > 0 ? Math.max(...allValues) : 0;
  const minValue = allValues.length > 0 ? Math.min(...allValues) : 0;

  // 为数据添加样式属性
  const processDataWithStyles = (node: TreeNode): any => {
    const processedNode: any = {
      name: node.name,
      value: node.value
    };

    // 根据数据值计算样式
    if (node.value === undefined || maxValue === minValue) {
      // 无数据时的正常样式
      processedNode.symbolSize = symbolSize;
      processedNode.itemStyle = {
        color: '#91cc75', // 默认绿色
        borderColor: '#ccc',
        borderWidth: 1
      };
      processedNode.label = {
        backgroundColor: '#fff',
        color: '#333',
        borderColor: '#ccc',
        borderWidth: 5,
        fontWeight: 'normal'
      };
    } else {
      // 根据数值大小计算样式
      const ratio = (node.value - minValue) / (maxValue - minValue);

      // 节点大小
      const nodeSize = symbolSize * (0.8 + ratio * 2.4);

      // 颜色
      const baseBlue = 100;
      const colorRange = 155;
      const redGreen = Math.floor(baseBlue - ratio * colorRange * 0.6); 
      const blue = Math.floor(200 + ratio * 55);
      const color = `rgb(${redGreen}, ${redGreen}, ${blue})`;

      // 边框颜色
      const borderRed = Math.floor(80 - ratio * 60);
      const borderGreen = Math.floor(80 - ratio * 60);
      const borderBlue = Math.floor(180 + ratio * 75);
      const borderColor = `rgb(${borderRed}, ${borderGreen}, ${borderBlue})`;

      processedNode.symbolSize = nodeSize;
      processedNode.itemStyle = {
        color: color,
        borderColor: borderColor,
        borderWidth: 1 + ratio * 2 //
      };
      processedNode.label = {
        backgroundColor: ratio > 0.3 ? '#d1e7ff' : '#f0f8ff', 
        color: ratio > 0.5 ? '#1a365d' : '#2d3748',
        borderColor: borderColor,
        borderWidth: 1,
        fontWeight: ratio > 0.6 ? 'bold' : 'normal'
      };
    }

    // 递归处理子节点
    if (node.children) {
      processedNode.children = node.children.map(child => processDataWithStyles(child));
    }

    return processedNode;
  };

  const processedData = processDataWithStyles(data);

  const option = {
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params: any) => {
        const { name, value } = params.data;
        return `
          <div style="font-weight: bold; margin-bottom: 4px;">${name}</div>
          ${value !== undefined ? `<div>值: ${value}</div>` : '<div style="color: #999;">无数据</div>'}
        `;
      }
    },
    series: [
      {
        type: 'tree',
        id: 0,
        name: 'tree1',
        data: [processedData],
        top: '5%',
        left: '15%',
        bottom: '5%',
        right: '40%',
        roam: true,
        edgeShape: edgeShape,
        edgeForkPosition: edgeForkPosition,
        initialTreeDepth: initialTreeDepth,
        nodeScaleRatio: nodeSpacing,
        orient: 'LR',
        layout: 'orthogonal',
        lineStyle: {
          width: 2,
          color: '#ccc'
        },
        label: {
          position: 'bottom',
          verticalAlign: 'middle',
          align: 'center',
          fontSize: 12,
          borderRadius: 2,
          padding: [2, 4],
          distance: 16,
          rotate: 0,
        },
        leaves: {
          label: {
            position: 'right',
            verticalAlign: 'middle',
            align: 'left',
            distance: 8,
            padding: [2, 4],
            rotate: 0
          }
        },
        emphasis: {
          focus: 'descendant',
          lineStyle: {
            width: 3,
            color: '#5470c6'
          },
          label: {
            backgroundColor: '#e6f3ff',
            borderColor: '#5470c6'
          }
        },
        expandAndCollapse: expandAndCollapse,
        animationDuration: animationDuration,
        animationDurationUpdate: animationDurationUpdate,
        zoom: zoomLevel
      }
    ]
  };

  return (
    <div className="w-full h-full relative">
      <ReactECharts
        ref={chartRef}
        option={option}
        className='overflow-hidden'
        style={{ height: 'calc(100% - 20px)', width: '100%' }}
      />
    </div>
  );
}));

export default TreeMap;
