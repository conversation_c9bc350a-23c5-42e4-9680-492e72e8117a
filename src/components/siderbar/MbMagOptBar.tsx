"use client"
import React from 'react'
import { AiFillHome, AiFillNotification } from 'react-icons/ai'
import { GiCube } from 'react-icons/gi'
import { ImPower } from 'react-icons/im'
import { RiDiscussFill, RiRestaurant2Fill } from 'react-icons/ri'

/**
 * 移动端管理端操作栏组件
 * 
 * <AUTHOR>
 * @date 2025/07/07
 * @returns 移动端管理端操作栏
 */
const MbMagOptBar: React.FC = () => {
  return (
    <>
      <ul className='grid grid-cols-3 gap-x-6 gap-y-4 items-center justify-center w-60 pl-1 pr-1'>
        <li className='flex flex-col items-center justify-center mb-click w-15 h-15'>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center`}>
            <AiFillHome size={25} />
          </div>
          <div className='text-xs mt-1 select-none'>主页</div>
        </li>
        <li className='flex flex-col items-center justify-center mb-click w-15 h-15'>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center`}>
            <AiFillNotification size={25} />
          </div>
          <div className='text-xs mt-1 select-none'>信息发布</div>
        </li>
        <li className='flex flex-col items-center justify-center mb-click w-15 h-15'>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center`}>
            <RiRestaurant2Fill size={25} />
          </div>
          <div className='text-xs mt-1 select-none'>服务运营</div>
        </li>
        <li className='flex flex-col items-center justify-center mb-click w-15 h-15'>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center`}>
            <ImPower size={20} />
          </div>
          <div className='text-xs mt-1 select-none'>能源管控</div>
        </li>
        <li className='flex flex-col items-center justify-center mb-click w-15 h-15'>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center`}>
            <GiCube size={25} />
          </div>
          <div className='text-xs mt-1 select-none'>设施监管</div>
        </li>
        <li className='flex flex-col items-center justify-center mb-click w-15 h-15'>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center`}>
            <RiDiscussFill size={25} />
          </div>
          <div className='text-xs mt-1 select-none'>反馈中心</div>
        </li>
      </ul>
    </>
  )
}

export default MbMagOptBar
