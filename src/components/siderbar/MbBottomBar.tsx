"use client"
import { Divider, Popover } from 'antd'
import React, { useContext } from 'react'
import { BsPersonFill } from 'react-icons/bs'
import { HiMenu } from 'react-icons/hi'
import { RiSettings6Fill } from 'react-icons/ri'
import { SiderBarInfoContext } from '../context/SiderBarInfoContext';
import { FaInfo } from 'react-icons/fa'
import MbStuOptBar from './MbStuOptBar'
import MbMagOptBar from './MbMagOptBar'

/**
 * 移动端底部导航栏组件
 * 
 * <AUTHOR>
 * @date 2025/07/07
 * @returns 移动端底部导航栏
 */
const MbBottomBar: React.FC = () => {

  const { switchMode, setSwitchMode } = useContext(SiderBarInfoContext);

  return (
    <div className="flex flex-row items-center justify-around h-full w-full select-none">
      <ul className='flex flex-row items-center justify-around h-full w-full'>
        <li className='flex flex-col items-center justify-center'>
          <Popover
            placement='topLeft'
            trigger={'click'}
            color='#155dfc'
            arrow={{ pointAtCenter: true }}
            content={
              <div className='block w-60 text-white select-none'>
                {switchMode == 'student' ? <MbStuOptBar /> : <MbMagOptBar />}
                <div>
                  <Divider className='bg-white/30' />
                  <div className='flex items-center justify-around'>
                    <div
                      className='flex flex-col items-center justify-center mb-click'
                      onClick={() => setSwitchMode(switchMode == 'student' ? 'admin' : 'student')}
                    >
                      <RiSettings6Fill
                        className={`${switchMode == 'student' ? 'rotate-0' : 'rotate-60'} transition-all duration-300 ease-in-out`}
                        size={25}
                      />
                      <div className='text-xs mt-1'>切换界面</div>
                    </div>
                    <div className='flex flex-col items-center justify-center mb-click'>
                      <FaInfo size={25}/>
                      <div className='text-xs mt-1'>关于我们</div>
                    </div>
                  </div>
                </div>
              </div>
            }
            zIndex={30}
          >
            <div className='flex flex-col items-center justify-center mb-click'>
              <HiMenu size={35} />
            </div>
          </Popover>
        </li>
        <li className='flex flex-col items-center justify-center mb-click'>
          <BsPersonFill size={35} />
        </li>
      </ul>
    </div>
  )
}

export default MbBottomBar
