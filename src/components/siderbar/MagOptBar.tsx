"use client"
import React, { useContext } from 'react'
import { AiFillHome, AiFillNotification } from 'react-icons/ai'
import { GiCube } from 'react-icons/gi'
import { ImPower } from 'react-icons/im'
import { RiDiscussFill, RiRestaurant2Fill } from 'react-icons/ri'
import { SiderBarInfoContext } from '../context/SiderBarInfoContext'
import Link from 'next/link'

/**
 * 管理端操作栏组件
 * 
 * <AUTHOR>
 * @date 2025/07/04
 * @returns 管理端操作栏
 */
const MagOptBar: React.FC = () => {

  const { selectedKey, setSelectedKey } = useContext(SiderBarInfoContext);

  return (
    <div className="w-full h-full">
      <ul className='flex flex-col items-center justify-center h-full gap-4 overflow-y-auto
        max-sm:flex-row max-sm:items-center max-sm:justify-around max-sm:w-full max-sm:h-full max-sm:gap-1 max-sm:px-2'>
        <Link
          href='/admin/overview'
          className='block'
        >
          <li
            className={`${selectedKey != '主页' && 'opacity-75 cursor-pointer'} flex flex-col items-center justify-center hover:opacity-100 transition-all duration-300 ease-out
            max-sm:flex-1`}
            onClick={() => setSelectedKey('主页')}
          >
            <div className={`${selectedKey == '主页' ? 'bg-gray-100/20' : 'bg-transparent'} w-12 h-12 rounded-full flex items-center justify-center
              max-sm:w-8 max-sm:h-8`}>
                <AiFillHome size={25} className="max-sm:!w-4 max-sm:!h-4"/>
            </div>
            <div className='text-xs mt-1 select-none max-sm:text-[10px] max-sm:mt-0.5 max-sm:leading-tight'>主页</div>
          </li>
        </Link>
        <Link
          href='/admin/publish'
          className='block'
        >
          <li
            className={`${selectedKey != '信息发布' && 'opacity-75 cursor-pointer'} flex flex-col items-center justify-center hover:opacity-100 transition-all duration-300 ease-out
            max-sm:flex-1`}
            onClick={() => setSelectedKey('信息发布')}
          >
            <div className={`${selectedKey == '信息发布' ? 'bg-gray-100/20' : 'bg-transparent'} w-12 h-12 rounded-full flex items-center justify-center
              max-sm:w-8 max-sm:h-8`}>
                <AiFillNotification size={25} className="max-sm:!w-4 max-sm:!h-4"/>
            </div>
            <div className='text-xs mt-1 select-none max-sm:text-[10px] max-sm:mt-0.5 max-sm:leading-tight'>信息发布</div>
          </li>
        </Link>
        <Link
          href='/admin/consume'
          className='block'
        >
          <li
            className={`${selectedKey != '服务运营' && 'opacity-75 cursor-pointer'} flex flex-col items-center justify-center hover:opacity-100 transition-all duration-300 ease-out
            max-sm:flex-1`}
            onClick={() => setSelectedKey('服务运营')}
          >
            <div className={`${selectedKey == '服务运营' ? 'bg-gray-100/20' : 'bg-transparent'} w-12 h-12 rounded-full flex items-center justify-center
              max-sm:w-8 max-sm:h-8`}>
                <RiRestaurant2Fill size={28} className="max-sm:!w-4 max-sm:!h-4"/>
            </div>
            <div className='text-xs mt-1 select-none max-sm:text-[10px] max-sm:mt-0.5 max-sm:leading-tight'>服务运营</div>
          </li>
        </Link>
        <Link
          href='/admin/sources'
          className='block'
        >
          <li
            className={`${selectedKey != '能源管控' && 'opacity-75 cursor-pointer'} flex flex-col items-center justify-center hover:opacity-100 transition-all duration-300 ease-out
            max-sm:flex-1`}
            onClick={() => setSelectedKey('能源管控')}
          >
            <div className={`${selectedKey == '能源管控' ? 'bg-gray-100/20' : 'bg-transparent'} w-12 h-12 rounded-full flex items-center justify-center
              max-sm:w-8 max-sm:h-8`}>
                <ImPower size={20} className="max-sm:!w-4 max-sm:!h-4"/>
            </div>
            <div className='text-xs mt-1 select-none max-sm:text-[10px] max-sm:mt-0.5 max-sm:leading-tight'>能源管控</div>
          </li>
        </Link>
        <Link
          href='/admin/facility'
          className='block'
        >
          <li
            className={`${selectedKey != '设施监管' && 'opacity-75 cursor-pointer'} flex flex-col items-center justify-center hover:opacity-100 transition-all duration-300 ease-out
            max-sm:flex-1`}
            onClick={() => setSelectedKey('设施监管')}
          >
            <div className={`${selectedKey == '设施监管' ? 'bg-gray-100/20' : 'bg-transparent'} w-12 h-12 rounded-full flex items-center justify-center
              max-sm:w-8 max-sm:h-8`}>
                <GiCube size={25} className="max-sm:!w-4 max-sm:!h-4"/>
            </div>
            <div className='text-xs mt-1 select-none max-sm:text-[10px] max-sm:mt-0.5 max-sm:leading-tight'>设施监管</div>
          </li>
        </Link>
        <Link
          href='/admin/feedback'
          className='block'
        >
          <li
            className={`${selectedKey != '反馈中心' && 'opacity-75 cursor-pointer'} flex flex-col items-center justify-center hover:opacity-100 transition-all duration-300 ease-out
            max-sm:flex-1`}
            onClick={() => setSelectedKey('反馈中心')}
          >
            <div className={`${selectedKey == '反馈中心' ? 'bg-gray-100/20' : 'bg-transparent'} w-12 h-12 rounded-full flex items-center justify-center
              max-sm:w-8 max-sm:h-8`}>
                <RiDiscussFill size={25} className="max-sm:!w-4 max-sm:!h-4"/>
            </div>
            <div className='text-xs mt-1 select-none max-sm:text-[10px] max-sm:mt-0.5 max-sm:leading-tight'>反馈中心</div>
          </li>
        </Link>
        </ul>
    </div>
  )
}

export default MagOptBar
