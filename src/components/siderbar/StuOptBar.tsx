"use client"
import React, { useContext } from 'react'
import { FaClipboard, FaHeart } from 'react-icons/fa'
import { BsBatteryHalf } from 'react-icons/bs'
import { HiChartBar } from 'react-icons/hi'
import { RiFeedbackFill, RiStarSmileFill } from 'react-icons/ri'
import { SiderBarInfoContext } from '../context/SiderBarInfoContext'
import Link from 'next/link'

/**
 * 学生端操作栏组件
 * 
 * <AUTHOR>
 * @date 2025/07/04
 * @returns 学生端操作栏
 */
const StuOptBar: React.FC = () => {

  const { selectedKey, setSelectedKey } = useContext(SiderBarInfoContext);

  return (
    <>
    <ul
      className='flex flex-col items-center justify-center h-full overflow-y-auto flex-1
      max-sm:flex-row max-sm:items-center max-sm:justify-around max-sm:w-full max-sm:h-full max-sm:gap-1 max-sm:px-2'
    >
      <Link
        href='/board/news'
        className='block'
      >
        <li
          className={`${selectedKey != '公告栏' && 'opacity-75 cursor-pointer'} flex flex-col items-center justify-center hover:opacity-100 transition-all duration-300 ease-out
          max-sm:flex-1`}
          onClick={() => setSelectedKey('公告栏')}
        >
          <div className={`${selectedKey == '公告栏' ? 'bg-gray-100/20' : 'bg-transparent'} w-12 h-12 rounded-full flex items-center justify-center
            max-sm:w-8 max-sm:h-8`}>
            <FaClipboard size={25} className="max-sm:!w-4 max-sm:!h-4"/>
          </div>
          <div className='text-xs mt-1 select-none max-sm:text-[10px] max-sm:mt-0.5 max-sm:leading-tight'>公告栏</div>
        </li>
      </Link>
      <Link
        href='/health'
        className='block'
      >
      <li
        className={`${selectedKey != '健康在线' && 'opacity-75 cursor-pointer'} flex flex-col items-center justify-center mt-4 hover:opacity-100 transition-all duration-300 ease-out
        max-sm:mt-0 max-sm:flex-1`}
        onClick={() => setSelectedKey('健康在线')}
      >
        <div className={`${selectedKey == '健康在线' ? 'bg-gray-100/20' : 'bg-transparent'} w-12 h-12 rounded-full flex items-center justify-center
          max-sm:w-8 max-sm:h-8`}>
          <FaHeart size={25} className="max-sm:!w-4 max-sm:!h-4"/>
        </div>
        <div className='text-xs mt-1 select-none max-sm:text-[10px] max-sm:mt-0.5 max-sm:leading-tight'>健康在线</div>
      </li>
      </Link>
      <Link
        href='/energy'
        className='block'
      >
        <li
          className={`${selectedKey != '宿舍能耗' && 'opacity-75 cursor-pointer'} flex flex-col items-center justify-center mt-4 hover:opacity-100 transition-all duration-300 ease-out
          max-sm:mt-0 max-sm:flex-1`}
          onClick={() => setSelectedKey('宿舍能耗')}
        >
          <div className={`${selectedKey == '宿舍能耗' ? 'bg-gray-100/20' : 'bg-transparent'} w-12 h-12 rounded-full flex items-center justify-center
            max-sm:w-8 max-sm:h-8`}>
            <BsBatteryHalf size={28} className="max-sm:!w-4 max-sm:!h-4"/>
          </div>
          <div className='text-xs mt-1 select-none max-sm:text-[10px] max-sm:mt-0.5 max-sm:leading-tight'>宿舍能耗</div>
        </li>
      </Link>
      <Link
        href='/repast'
        className='block'
      >
        <li
          className={`${selectedKey != '就餐热度' && 'opacity-75 cursor-pointer'} flex flex-col items-center justify-center mt-4 hover:opacity-100 transition-all duration-300 ease-out
          max-sm:mt-0 max-sm:flex-1`}
          onClick={() => setSelectedKey('就餐热度')}
        >
          <div className={`${selectedKey == '就餐热度' ? 'bg-gray-100/20' : 'bg-transparent'} w-12 h-12 rounded-full flex items-center justify-center
            max-sm:w-8 max-sm:h-8`}>
            <HiChartBar size={28} className="max-sm:!w-4 max-sm:!h-4"/>
          </div>
          <div className='text-xs mt-1 select-none max-sm:text-[10px] max-sm:mt-0.5 max-sm:leading-tight'>就餐热度</div>
        </li>
      </Link>
      <Link
        href='/stars'
        className='block'
      >
        <li
          className={`${selectedKey != '育人星光' && 'opacity-75 cursor-pointer'} flex flex-col items-center justify-center mt-4 hover:opacity-100 transition-all duration-300 ease-out
          max-sm:mt-0 max-sm:flex-1`}
          onClick={() => setSelectedKey('育人星光')}
        >
          <div className={`${selectedKey == '育人星光' ? 'bg-gray-100/20' : 'bg-transparent'} w-12 h-12 rounded-full flex items-center justify-center
            max-sm:w-8 max-sm:h-8`}>
            <RiStarSmileFill size={28} className="max-sm:!w-4 max-sm:!h-4"/>
          </div>
          <div className='text-xs mt-1 select-none max-sm:text-[10px] max-sm:mt-0.5 max-sm:leading-tight'>育人星光</div>
        </li>
      </Link>
      <Link
        href='/feedback'
        className='block'
      >
        <li
          className={`${selectedKey != '问题反馈' && 'opacity-75 cursor-pointer'} flex flex-col items-center justify-center mt-4 hover:opacity-100 transition-all duration-300 ease-out
          max-sm:mt-0 max-sm:flex-1`}
          onClick={() => setSelectedKey('问题反馈')}
        >
          <div className={`${selectedKey == '问题反馈' ? 'bg-gray-100/20' : 'bg-transparent'} w-12 h-12 rounded-full flex items-center justify-center
            max-sm:w-8 max-sm:h-8`}>
            <RiFeedbackFill size={28} className="max-sm:!w-4 max-sm:!h-4"/>
          </div>
          <div className='text-xs mt-1 select-none max-sm:text-[10px] max-sm:mt-0.5 max-sm:leading-tight'>问题反馈</div>
        </li>
      </Link>
    </ul>
    </>
  )
}

export default StuOptBar
