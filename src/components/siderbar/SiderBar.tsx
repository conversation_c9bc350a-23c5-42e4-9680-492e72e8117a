"use client"
import { Popover } from 'antd'
import React, { useContext, useEffect, useState } from 'react'
import { BsPersonFill } from 'react-icons/bs'
import { RiSettings6Fill } from 'react-icons/ri'
import { motion, AnimatePresence } from 'framer-motion'
import { SiderBarInfoContext } from '../context/SiderBarInfoContext';
import StuOptBar from './StuOptBar'
import MagOptBar from './MagOptBar'

/**
 * 侧边栏组件
 * 
 * <AUTHOR>
 * @date 2025/07/04
 * @returns 侧边栏
 */
const SiderBar: React.FC = () => {

  const { switchMode, setSwitchMode } = useContext(SiderBarInfoContext);
  const [ isFirstRender, setIsFirstRender ] = useState(true);

  // 首次渲染时不渐入
  useEffect(() => {
    setIsFirstRender(prev => false);
  }, [])

  return (
    <div
      className='flex flex-col gap-2 mt-8 relative top-0 h-full
      max-sm:flex-row max-sm:mt-0 max-sm:w-full max-sm:h-full max-sm:items-center max-sm:justify-center max-sm:px-4'
    >
      {/* 导航项目区域 */}
      <div className="flex-1 max-sm:flex max-sm:w-full max-sm:h-full max-sm:items-center max-sm:justify-center">
        <AnimatePresence mode="wait">
          {switchMode === 'student' && (
            <motion.div
              key="student"
              initial={isFirstRender ? false : { opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{
                duration: 0.2,
                ease: [0.4, 0, 0.2, 1]
              }}
              className="max-sm:w-full max-sm:h-full"
            >
              <StuOptBar />
            </motion.div>
          )}
          {switchMode === 'admin' && (
            <motion.div
              key="admin"
              initial={isFirstRender ? false : { opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{
                duration: 0.2,
                ease: [0.4, 0, 0.2, 1]
              }}
              className="max-sm:w-full max-sm:h-full"
            >
              <MagOptBar />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 控制按钮区域 */}
      <div className='mt-auto pt-8 mb-12 bottom-5 w-full flex flex-col h-25 items-center justify-center
        max-sm:mt-0 max-sm:pt-0 max-sm:mb-0 max-sm:flex-row max-sm:gap-4 max-sm:h-full max-sm:w-auto'>
        <Popover
          placement='rightBottom'
          color='#1A237E'
          content={
            <div className='text-xs font-bold text-center text-white'>
              {switchMode == 'student' ? '切换至管理端界面' : '切换至学生端界面'}
            </div>
          }
        >
          <motion.div
            className="mt-3 mb-6 cursor-pointer max-sm:mt-0 max-sm:mb-0"
            onClick={() => setSwitchMode(switchMode == 'student' ? 'admin' : 'student')}
            animate={{
              rotate: switchMode === 'student' ? 0 : 60,
              opacity: 0.6
            }}
            whileHover={{
              scale: 1.1,
              opacity: 1,
              rotate: switchMode === 'student' ? 60 : 0
            }}
            whileTap={{ scale: 0.9 }}
            transition={{
              duration: 0.2,
              ease: [0.4, 0, 0.2, 1]
            }}
          >
            <RiSettings6Fill size={30} className="max-sm:!w-6 max-sm:!h-6"/>
          </motion.div>
        </Popover>
        <Popover
          placement='rightBottom'
          color='#1A237E'
          content={
            <div className='text-sm font-bold text-center text-white'>
              点击并完成登录认证后<br />可使用完整功能
            </div>
          }
          autoAdjustOverflow
        >
          <motion.div
            className='w-full flex flex-col items-center justify-center cursor-pointer max-sm:w-auto'
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            transition={{
              duration: 0.15,
              ease: [0.4, 0, 0.2, 1]
            }}
          >
              <div className='w-12 h-12 rounded-full bg-gray-100/20 flex items-center justify-center
                max-sm:w-10 max-sm:h-10'>
                <BsPersonFill size={28} className="max-sm:!w-5 max-sm:!h-5"/>
              </div>
          </motion.div>
        </Popover>
      </div>
    </div>
  )
}

export default SiderBar;
