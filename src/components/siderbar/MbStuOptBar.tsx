"use client"
import React from 'react'
import { BsBatteryHalf } from 'react-icons/bs'
import { FaClipboard, FaHeart } from 'react-icons/fa'
import { HiChartBar } from 'react-icons/hi'
import { RiFeedbackFill, RiStarSmileFill } from 'react-icons/ri'

/**
 * 移动端学生端操作栏组件
 * 
 * <AUTHOR>
 * @date 2025/07/07
 * @returns 移动端学生端操作栏
 */
const MbStuOptBar: React.FC = () => {
  return (
    <>
      <ul className='grid grid-cols-3 gap-x-6 gap-y-4 items-center justify-center w-60 pl-1 pr-1'>
        <li className='flex flex-col items-center justify-center mb-click w-15 h-15'>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center`}>
            <FaClipboard size={25} />
          </div>
          <div className='text-xs mt-1 select-none'>公告栏</div>
        </li>
        <li className='flex flex-col items-center justify-center mb-click w-15 h-15'>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center`}>
            <FaHeart size={25} />
          </div>
          <div className='text-xs mt-1 select-none'>健康在线</div>
        </li>
        <li className='flex flex-col items-center justify-center mb-click w-15 h-15'>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center`}>
            <BsBatteryHalf size={30} />
          </div>
          <div className='text-xs mt-1 select-none'>宿舍能耗</div>
        </li>
        <li className='flex flex-col items-center justify-center mb-click w-15 h-15'>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center`}>
            <HiChartBar size={25} />
          </div>
          <div className='text-xs mt-1 select-none'>就餐热度</div>
        </li>
        <li className='flex flex-col items-center justify-center mb-click w-15 h-15'>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center`}>
            <RiStarSmileFill size={25} />
          </div>
          <div className='text-xs mt-1 select-none'>育人星光</div>
        </li>
        <li className='flex flex-col items-center justify-center mb-click w-15 h-15'>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center`}>
            <RiFeedbackFill size={25} />
          </div>
          <div className='text-xs mt-1 select-none'>问题反馈</div>
        </li>
      </ul>
    </>
  )
}

export default MbStuOptBar
