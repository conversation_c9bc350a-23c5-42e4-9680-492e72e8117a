"use client"
import React, { createContext, useContext, ReactNode } from 'react';
import { message } from 'antd';
import type { MessageInstance } from 'antd/es/message/interface';

/**
 * 消息上下文类型定义
 */
interface MessageContextType {
  messageApi: MessageInstance;
}

/**
 * 消息上下文
 */
const MessageContext = createContext<MessageContextType | undefined>(undefined);

/**
 * 消息提供者组件属性
 */
interface MessageProviderProps {
  children: ReactNode;
}

/**
 * 消息提供者组件
 *
 * <AUTHOR>
 * @date 2025/07/18
 * @description 提供全局消息API的上下文组件
 * @param props - 组件属性
 * @param props.children - 子组件
 * @returns 消息提供者组件
 */
const MessageProvider: React.FC<MessageProviderProps> = ({ children }) => {
  const [messageApi, contextHolder] = message.useMessage();

  const value: MessageContextType = {
    messageApi
  };

  return (
    <MessageContext.Provider value={value}>
      {contextHolder}
      {children}
    </MessageContext.Provider>
  );
};

/**
 * 使用消息上下文的Hook
 *
 * <AUTHOR>
 * @date 2025/07/18
 * @description 获取消息API的自定义Hook
 * @returns 消息API实例
 * @throws 当在MessageProvider外部使用时抛出错误
 */
const useMessage = (): MessageInstance => {
  const context = useContext(MessageContext);

  if (context === undefined) {
    throw new Error('useMessage must be used within a MessageProvider');
  }

  return context.messageApi;
};

/**
 * 消息工具类
 * 提供常用的消息提示方法
 *
 * <AUTHOR>
 * @date 2025/07/18
 */
export class MessageUtils {
  private static messageApi: MessageInstance | null = null;

  /**
   * 设置消息API实例
   * @param api - 消息API实例
   */
  static setMessageApi(api: MessageInstance) {
    this.messageApi = api;
  }

  /**
   * 显示成功消息
   * @param content - 消息内容
   * @param duration - 显示时长（秒）
   * @param key - 消息唯一标识
   */
  static success(content: string, duration: number = 3, key?: string) {
    if (this.messageApi) {
      this.messageApi.success({
        content,
        duration,
        key
      });
    }
  }

  /**
   * 显示错误消息
   * @param content - 消息内容
   * @param duration - 显示时长（秒）
   * @param key - 消息唯一标识
   */
  static error(content: string, duration: number = 5, key?: string) {
    if (this.messageApi) {
      this.messageApi.error({
        content,
        duration,
        key
      });
    }
  }

  /**
   * 显示警告消息
   * @param content - 消息内容
   * @param duration - 显示时长（秒）
   * @param key - 消息唯一标识
   */
  static warning(content: string, duration: number = 4, key?: string) {
    if (this.messageApi) {
      this.messageApi.warning({
        content,
        duration,
        key
      });
    }
  }

  /**
   * 显示信息消息
   * @param content - 消息内容
   * @param duration - 显示时长（秒）
   * @param key - 消息唯一标识
   */
  static info(content: string, duration: number = 3, key?: string) {
    if (this.messageApi) {
      this.messageApi.info({
        content,
        duration,
        key
      });
    }
  }

  /**
   * 显示加载消息
   * @param content - 消息内容
   * @param duration - 显示时长（秒，0表示不自动关闭）
   * @param key - 消息唯一标识
   */
  static loading(content: string, duration: number = 0, key?: string) {
    if (this.messageApi) {
      this.messageApi.loading({
        content,
        duration,
        key
      });
    }
  }

  /**
   * 销毁所有消息
   */
  static destroy() {
    if (this.messageApi) {
      this.messageApi.destroy();
    }
  }

  /**
   * 销毁指定key的消息
   * @param key - 消息唯一标识
   */
  static destroyByKey(key: string) {
    if (this.messageApi) {
      this.messageApi.destroy(key);
    }
  }
}

export { MessageProvider, useMessage, MessageContext };
export type { MessageContextType, MessageProviderProps };