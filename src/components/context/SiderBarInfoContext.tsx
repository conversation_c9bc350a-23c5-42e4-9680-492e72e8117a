"use client"
import React, { createContext, useState, ReactNode, useEffect } from 'react';
import { usePathname } from 'next/navigation';

/* 信息上下文类型 */
interface SiderBarInfoContextType {
  isPanelOpen: boolean;                               // 二级菜单是否展开
  selectedPanel: string;                              // 当前选中的二级菜单
  selectedKey: string;                                // 二级菜单选中项
  switchMode: string;                                 // 学生/管理模式
  setIsPanelOpen: (isPanelOpen: boolean) => void;
  setSelectedPanel: (selectedPanel: string) => void;
  setSelectedKey: (selectedKey: string) => void;
  setSwitchMode: (switchMode: string) => void;
}

/* 信息上下文 */
const SiderBarInfoContext = createContext<SiderBarInfoContextType>({
  isPanelOpen: true,
  selectedPanel: '新闻动态',
  selectedKey: '公告栏',
  switchMode: 'student',
  setIsPanelOpen: () => {},
  setSelectedPanel: () => {},
  setSelectedKey: () => {},
  setSwitchMode: () => {}
});

interface SiderBarInfoProviderProps {
  children: ReactNode;
}

/* 路径映射子面板 */
const panelMap: Record<string, string> = {
  '/board/news': '新闻动态',
  '/board/notice': '通知公告',
  '/board/giveback': '失物招领',
  '/board/market': '二手市场',
  '/admin/overview/power': '用电看板',
  '/admin/overview/flow': '人流量看板',
  '/admin/sources': '用电管理'
}

/* 路径映射选项卡 */
const keyMap: Record<string, string> = {
  /* 学生端 */
  'board': '公告栏',
  'health': '健康在线',
  'energy': '宿舍能耗',
  'repast': '就餐热度',
  'stars': '育人星光',
  'feedback': '问题反馈',
  /* 管理端 */
  'overview': '主页',
  'publish': '信息发布',
  'consume': '服务运营',
  'sources': '能源管控',
  'facility': '设施监管',
}

/* 提供上下文 */
const SiderBarInfoProvider: React.FC<SiderBarInfoProviderProps> = ({ children }) => {

  const [ isPanelOpen, setIsPanelOpen ] = useState(true);

  const pathname: string = usePathname();
  const pathPrefix: string = pathname.startsWith('/admin') ? pathname.split('/')[2] : pathname.split('/')[1];

  const [ switchMode, setSwitchMode ] = useState(pathname.startsWith('/admin') ? 'admin' : 'student');
  const [ selectedPanel, setSelectedPanel ] = useState(panelMap[pathname]);
  const [ selectedKey, setSelectedKey ] = useState(keyMap[pathPrefix]);

  const value: SiderBarInfoContextType = {
    isPanelOpen,
    selectedPanel,
    selectedKey,
    switchMode,
    setIsPanelOpen,
    setSelectedPanel,
    setSelectedKey,
    setSwitchMode
  };

  useEffect(() => {
    // 路径变化时更新选中面板
    setSelectedPanel(panelMap[pathname]);
  }, [pathname])

  return (
    <SiderBarInfoContext value={value}>
      { children }
    </SiderBarInfoContext>
  );
};

export { SiderBarInfoContext, SiderBarInfoProvider };