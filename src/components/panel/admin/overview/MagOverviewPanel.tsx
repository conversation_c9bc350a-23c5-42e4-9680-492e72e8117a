"use client"
import { <PERSON><PERSON>, Popover } from 'antd';
import React, { useContext } from 'react'
import { BsChevronBarLeft, BsChevronBarRight } from 'react-icons/bs';
import { SiderBarInfoContext } from '../../../context/SiderBarInfoContext';
import Link from 'next/link';
import { ImPowerCord } from 'react-icons/im';
import { HiUserGroup } from 'react-icons/hi';
import { RiListSettingsLine } from 'react-icons/ri';

/**
 * 管理端首页子面板组件
 * 子版块如下：
 * 1. 用电看板栏
 * 2. 人流量看板栏
 * 
 * <AUTHOR>
 * @date 2025/07/05
 * @returns 管理端首页子面板
 */
const MagOverviewPanel: React.FC = () => {

  // 从上下文获取信息
  const { isPanelOpen, setIsPanelOpen, selectedPanel, setSelectedPanel } = useContext(SiderBarInfoContext);

  return (
    <>
    <div 
      className={`${isPanelOpen ?'min-w-60 p-8 border-r-1' : 'min-w-0 opacity-0 pt-8 pb-8 pointer-events-none'} flex flex-col flex-0 h-full border-gray-100 shadow-xs relative transition-all duration-300 ease-in
      max-sm:hidden text-nowrap`}
    >
      <ul className='flex flex-col gap-4'>
        {/* 用电看板 */}
        <Link
          href='/admin/overview/power'
          className='block'
        >
          <li
            className={`${selectedPanel == '用电看板' ? 'bg-indigo-400/20' : 'hover:bg-indigo-400/20 cursor-pointer'} 
              flex w-full gap-3 justify-start items-center pt-2 pb-2 pl-4 pr-4 rounded-2xl transition-all duration-300 ease-in-out select-none`}
            onClick={() => setSelectedPanel('用电看板')}
          >
            <div className='flex items-center justify-center'><ImPowerCord color={selectedPanel == '用电看板' ? '#536DFE' : '#333'} size={20}/></div>
            <div className={`${selectedPanel == '用电看板' && 'text-indigo-500'} flex items-center justify-center text-sm`}>用电看板</div>
          </li>
        </Link>
        {/* 人流量看板 */}
        <Link
          href='/admin/overview/flow'
          className='block'
        >
          <li
            className={`${selectedPanel == '人流量看板' ? 'bg-indigo-400/20' : 'hover:bg-indigo-400/20 cursor-pointer'}
              flex w-full gap-3 justify-start items-center pt-2 pb-2 pl-4 pr-4 rounded-2xl transition-all duration-300 ease-in-out select-none`}
            onClick={() => setSelectedPanel('人流量看板')}
          >
            <div className='flex items-center justify-center'><HiUserGroup color={selectedPanel == '人流量看板' ? '#536DFE' : '#333'} size={20} /></div>
            <div className={`${selectedPanel == '人流量看板' && 'text-indigo-500'} flex items-center justify-center text-sm`}>人流量看板</div>
          </li>
        </Link>
      </ul>
      <div className='flex mt-auto'>
        <Button color="geekblue" variant="solid" icon={<RiListSettingsLine size={20} style={{transform: 'translateY(2px)'}}/>} block>
          图表设定
        </Button>
      </div>
    </div>
    {/* 折叠按钮 */}
    <div 
      className='right-0 top-1/2 flex w-fit h-full items-center justify-center bg-gray-50 rounded-tl-xl rounded-bl-xl 
      max-sm:hidden'
    >
      { isPanelOpen ? 
        <Popover
          placement='right'
          color='#1A237E'
          mouseEnterDelay={1}
          mouseLeaveDelay={0}
          content={
            <div className='text-xs font-bold text-center text-white'>
              收起二级菜单
            </div>
          }
          destroyOnHidden
        >
          <BsChevronBarLeft
            className='opacity-30 hover:opacity-100 cursor-pointer transition-all duration-300 ease-in-out'
            size={20}
            onClick={() => setIsPanelOpen(!isPanelOpen)}
          />
        </Popover>
        : 
        <Popover
          placement='right'
          color='#1A237E'
          mouseEnterDelay={1}
          mouseLeaveDelay={0}
          content={
            <div className='text-xs font-bold text-center text-white'>
              打开二级菜单
            </div>
          }
          destroyOnHidden
        >
          <BsChevronBarRight
            className='opacity-30 hover:opacity-100 ml-1 cursor-pointer transition-all duration-300 ease-in-out'
            size={20}
            onClick={() => setIsPanelOpen(!isPanelOpen)}
          /> 
        </Popover>
      }
    </div>
    </>
  )
}

export default MagOverviewPanel;
