"use client"
import { Popover } from 'antd';
import React, { useContext } from 'react'
import { BsChevronBarLeft, BsChevronBarRight } from 'react-icons/bs';
import { FaGasPump } from 'react-icons/fa';
import { SiderBarInfoContext } from '../../../context/SiderBarInfoContext';
import Link from 'next/link';
import { GiHotSurface } from 'react-icons/gi';
import { IoMdWater } from 'react-icons/io';
import { ImPowerCord } from 'react-icons/im';

/**
 * 能源管控子面板组件
 * 子版块如下：
 * 1. 用电管理栏
 * 2. 用水管理栏
 * 3. 用暖管理栏
 * 4. 用气用油管理栏
 * 
 * <AUTHOR>
 * @date 2025/07/05
 * @returns 能源管控子面板
 */
const MagSourcesPanel: React.FC = () => {

  // 从上下文获取信息
  const { isPanelOpen, setIsPanel<PERSON><PERSON>, selectedPanel, setSelectedPanel } = useContext(SiderBarInfoContext);

  return (
    <>
    <div 
      className={`${isPanelOpen ?'min-w-60 p-8 border-r-1' : 'min-w-0 opacity-0 pt-8 pb-8 pointer-events-none'} flex flex-col flex-0 h-full border-gray-100 shadow-xs relative transition-all duration-300 ease-in
      max-sm:hidden text-nowrap`}
    >
      <ul className='flex flex-col gap-4'>
        {/* 用电管理 */}
        <Link
          href='/admin/sources'
          className='block'
        >
          <li
            className={`${selectedPanel == '用电管理' ? 'bg-indigo-400/20' : 'hover:bg-indigo-400/20 cursor-pointer'} 
              flex w-full gap-3 justify-start items-center pt-2 pb-2 pl-4 pr-4 rounded-2xl transition-all duration-300 ease-in-out select-none`}
            onClick={() => setSelectedPanel('用电管理')}
          >
            <div className='flex items-center justify-center'><ImPowerCord color={selectedPanel == '用电管理' ? '#536DFE' : '#333'} size={20}/></div>
            <div className={`${selectedPanel == '用电管理' && 'text-indigo-500'} flex items-center justify-center text-sm`}>用电管理</div>
          </li>
        </Link>
        {/* 用水管理 */}
        <Link
          href='/admin/sources'
          className='block'
        >
          <li
            className={`${selectedPanel == '用水管理' ? 'bg-indigo-400/20' : 'hover:bg-indigo-400/20 cursor-pointer'}
              flex w-full gap-3 justify-start items-center pt-2 pb-2 pl-4 pr-4 rounded-2xl transition-all duration-300 ease-in-out select-none`}
            onClick={() => setSelectedPanel('用水管理')}
          >
            <div className='flex items-center justify-center'><IoMdWater color={selectedPanel == '用水管理' ? '#536DFE' : '#333'} size={20} /></div>
            <div className={`${selectedPanel == '用水管理' && 'text-indigo-500'} flex items-center justify-center text-sm`}>用水管理</div>
          </li>
        </Link>
        {/* 用暖管理 */}
        <Link
          href='/admin/sources'
          className='block'
        >
          <li
            className={`${selectedPanel == '用暖管理' ? 'bg-indigo-400/20' : 'hover:bg-indigo-400/20 cursor-pointer'}
              flex w-full gap-3 justify-start items-center pt-2 pb-2 pl-4 pr-4 rounded-2xl transition-all duration-300 ease-in-out select-none`}
            onClick={() => setSelectedPanel('用暖管理')}
          >
            <div className='flex items-center justify-center'><GiHotSurface color={selectedPanel == '用暖管理' ? '#536DFE' : '#333'} size={20} /></div>
            <div className={`${selectedPanel == '用暖管理' && 'text-indigo-500'} flex items-center justify-center text-sm`}>用暖管理</div>
          </li>
        </Link>
        {/* 用气用油管理 */}
        <Link
          href='/admin/sources'
          className='block'
        >
          <li
            className={`${selectedPanel == '用气用油管理' ? 'bg-indigo-400/20 ' : 'hover:bg-indigo-400/20 cursor-pointer'}
              flex w-full gap-3 justify-start items-center pt-2 pb-2 pl-4 pr-4 rounded-2xl transition-all duration-300 ease-in-out select-none`}
            onClick={() => setSelectedPanel('用气用油管理')}
          >
            <div className='flex items-center justify-center'><FaGasPump color={selectedPanel == '用气用油管理' ? '#536DFE' : '#333'} size={19} /></div>
            <div className={`${selectedPanel == '用气用油管理' && 'text-indigo-500'} flex items-center justify-center text-sm`}>用气用油管理</div>
          </li>
        </Link>
      </ul>
    </div>
    {/* 折叠按钮 */}
    <div 
      className='right-0 top-1/2 flex w-fit h-full items-center justify-center bg-gray-50 rounded-tl-xl rounded-bl-xl 
      max-sm:hidden'
    >
      { isPanelOpen ? 
        <Popover
          placement='right'
          color='#1A237E'
          mouseEnterDelay={1}
          mouseLeaveDelay={0}
          content={
            <div className='text-xs font-bold text-center text-white'>
              收起二级菜单
            </div>
          }
          destroyOnHidden
        >
          <BsChevronBarLeft
            className='opacity-30 hover:opacity-100 cursor-pointer transition-all duration-300 ease-in-out'
            size={20}
            onClick={() => setIsPanelOpen(!isPanelOpen)}
          />
        </Popover>
        : 
        <Popover
          placement='right'
          color='#1A237E'
          mouseEnterDelay={1}
          mouseLeaveDelay={0}
          content={
            <div className='text-xs font-bold text-center text-white'>
              打开二级菜单
            </div>
          }
          destroyOnHidden
        >
          <BsChevronBarRight
            className='opacity-30 hover:opacity-100 ml-1 cursor-pointer transition-all duration-300 ease-in-out'
            size={20}
            onClick={() => setIsPanelOpen(!isPanelOpen)}
          /> 
        </Popover>
      }
    </div>
    </>
  )
}

export default MagSourcesPanel;
