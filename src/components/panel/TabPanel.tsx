import { ConfigProvider, Segmented } from 'antd';
import React from 'react';
import type { SegmentedProps } from 'antd';

/**
 * 选项卡组件
 * 
 * <AUTHOR>
 * @date 2025/07/09
 * @returns 选项卡
 */
const TabPanel: React.FC<SegmentedProps> = ({ options, ...props }) => {
  return (
    <ConfigProvider
      theme={{
        token: {
          borderRadius: 12,
          colorBgContainer: 'transparent',
          colorBgElevated: 'transparent',
        },
        components: {
          Segmented: {
            // 整体背景透明
            trackBg: 'rgba(255, 255, 255, 0.08)',
            trackPadding: 3,
            // 未选中项样式
            itemColor: 'rgba(255, 255, 255, 0.75)',
            itemHoverColor: 'rgba(255, 255, 255, 0.9)',
            itemHoverBg: 'rgba(255, 255, 255, 0.1)',
            itemActiveBg: 'rgba(255, 255, 255, 0.15)',
            // 选中项样式
            itemSelectedColor: 'rgba(255, 255, 255, 1)',
            itemSelectedBg: '#1a227e6e', 
            // 边框和圆角
            borderRadius: 12,
            borderRadiusSM: 10,
            borderRadiusLG: 14,
            // 字体权重
            fontWeightStrong: 600,
          }
        }
      }}
    >
      <Segmented
        options={options}
        shape='round'
        block
        {...props}
      />
    </ConfigProvider>
  )
}

export default TabPanel;
