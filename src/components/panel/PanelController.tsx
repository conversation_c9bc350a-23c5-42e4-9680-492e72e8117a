"use client"
import { usePathname } from 'next/navigation';
import React from 'react'
import StuBoardPanel from './student/board/StuBoardPanel';
import MagSourcesPanel from './admin/sources/MagSourcesPanel';
import MagOverviewPanel from './admin/overview/MagOverviewPanel';

/**
 * 面板控制器组件
 * 
 * 该组件用于根据当前路由路径动态渲染相应的组件
 * 它主要起到了解耦路由与组件渲染的作用，使得组件可以根据路由变化而动态更新
 * 
 * <AUTHOR>
 * @date 2025/07/05
 * @returns 根据路由路径动态渲染的组件
 */
const PanelController = () => {

  const pathname: string = usePathname();

  return (
    <>
      {/* 学生端 */}
      { pathname.startsWith('/board') && <StuBoardPanel /> }
      {/* 管理端 */}
      { pathname.startsWith('/admin/overview') && <MagOverviewPanel /> }
      { pathname.startsWith('/admin/sources') && <MagSourcesPanel /> }
    </>
  )
}

export default PanelController;
