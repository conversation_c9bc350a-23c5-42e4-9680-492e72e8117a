"use client"
import React from 'react';
import TabPanel from '../../TabPanel';

/**
 * 移动端公示栏子面板组件
 * 
 * <AUTHOR>
 * @date 2025/07/07
 * @returns 移动端公示栏子面板
 */

const options = [
  {
    label: '新闻动态',
    value: 'news',
  },
  {
    label: '通知公告',
    value: 'notice',
  },
  {
    label: '失物招领',
    value: 'lost',
  },
  {
    label: '二手市场',
    value: 'market',
  },
];

const MbStuBoardPanel: React.FC = () => {
  return (
    <div className='block mt-1 h-full w-full'>
      <TabPanel options={options} />
    </div>
  )
}

export default MbStuBoardPanel;