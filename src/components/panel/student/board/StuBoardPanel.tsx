"use client"
import { Popover } from 'antd';
import React, { useContext } from 'react'
import { BiDonateHeart, BiMessage, BiNews } from 'react-icons/bi';
import { BsChevronBarLeft, BsChevronBarRight } from 'react-icons/bs';
import { FaRegHandshake } from 'react-icons/fa';
import { SiderBarInfoContext } from '../../../context/SiderBarInfoContext';
import Link from 'next/link';

/**
 * 公示栏子面板组件
 * 子版块如下：
 * 顶部搜索栏元素：同时检索活动和通知
 * 1. 新闻动态栏
 * 2. 通知公告栏
 * 3. 失物招领栏
 * 4. 二手市场栏
 * 
 * <AUTHOR>
 * @date 2025/07/05
 * @returns 公示栏子面板
 */
const StuBoardPanel: React.FC = () => {

  // 从上下文获取信息
  const { isPanelOpen, setIsPanelOpen, selectedPanel, setSelectedPanel } = useContext(SiderBarInfoContext);

  return (
    <>
    <div 
      className={`${isPanelOpen ?'min-w-60 p-8 border-r-1' : 'min-w-0 opacity-0 pt-8 pb-8 pointer-events-none'} flex flex-col flex-0 h-full border-gray-100 shadow-xs relative transition-all duration-300 ease-in
      max-sm:hidden text-nowrap`}
    >
      <ul className='flex flex-col gap-4'>
        {/* 新闻动态 */}
        <Link
          href='/board/news'
          className='block'
        >
          <li
            className={`${selectedPanel == '新闻动态' ? 'bg-indigo-400/20' : 'hover:bg-indigo-400/20 cursor-pointer'} 
              flex w-full gap-3 justify-start items-center pt-2 pb-2 pl-4 pr-4 rounded-2xl transition-all duration-300 ease-in-out select-none`}
            onClick={() => setSelectedPanel('新闻动态')}
          >
            <div className='flex items-center justify-center'><BiNews color={selectedPanel == '新闻动态' ? '#536DFE' : '#333'} size={20}/></div>
            <div className={`${selectedPanel == '新闻动态' && 'text-indigo-500'} flex items-center justify-center text-sm`}>新闻动态</div>
          </li>
        </Link>
        {/* 通知公告 */}
        <Link
          href='/board/notice'
          className='block'
        >
          <li
            className={`${selectedPanel == '通知公告' ? 'bg-indigo-400/20' : 'hover:bg-indigo-400/20 cursor-pointer'}
              flex w-full gap-3 justify-start items-center pt-2 pb-2 pl-4 pr-4 rounded-2xl transition-all duration-300 ease-in-out select-none`}
            onClick={() => setSelectedPanel('通知公告')}
          >
            <div className='flex items-center justify-center'><BiMessage color={selectedPanel == '通知公告' ? '#536DFE' : '#333'} size={20} /></div>
            <div className={`${selectedPanel == '通知公告' && 'text-indigo-500'} flex items-center justify-center text-sm`}>通知公告</div>
          </li>
        </Link>
        {/* 失物招领 */}
        <Link
          href='/board/giveback'
          className='block'
        >
          <li
            className={`${selectedPanel == '失物招领' ? 'bg-indigo-400/20' : 'hover:bg-indigo-400/20 cursor-pointer'}
              flex w-full gap-3 justify-start items-center pt-2 pb-2 pl-4 pr-4 rounded-2xl transition-all duration-300 ease-in-out select-none`}
            onClick={() => setSelectedPanel('失物招领')}
          >
            <div className='flex items-center justify-center'><BiDonateHeart color={selectedPanel == '失物招领' ? '#536DFE' : '#333'} size={20} /></div>
            <div className={`${selectedPanel == '失物招领' && 'text-indigo-500'} flex items-center justify-center text-sm`}>失物招领</div>
          </li>
        </Link>
        {/* 二手市场 */}
        <Link
          href='/board/market'
          className='block'
        >
          <li
            className={`${selectedPanel == '二手市场' ? 'bg-indigo-400/20 ' : 'hover:bg-indigo-400/20 cursor-pointer'}
              flex w-full gap-3 justify-start items-center pt-2 pb-2 pl-4 pr-4 rounded-2xl transition-all duration-300 ease-in-out select-none`}
            onClick={() => setSelectedPanel('二手市场')}
          >
            <div className='flex items-center justify-center'><FaRegHandshake color={selectedPanel == '二手市场' ? '#536DFE' : '#333'} size={20} /></div>
            <div className={`${selectedPanel == '二手市场' && 'text-indigo-500'} flex items-center justify-center text-sm`}>二手市场</div>
          </li>
        </Link>
      </ul>
    </div>
    {/* 折叠按钮 */}
    <div 
      className='right-0 top-1/2 flex w-fit h-full items-center justify-center bg-gray-50 rounded-tl-xl rounded-bl-xl 
      max-sm:hidden'
    >
      { isPanelOpen ? 
        <Popover
          placement='right'
          color='#1A237E'
          mouseEnterDelay={1}
          mouseLeaveDelay={0}
          content={
            <div className='text-xs font-bold text-center text-white'>
              收起二级菜单
            </div>
          }
          destroyOnHidden
        >
          <BsChevronBarLeft
            className='opacity-30 hover:opacity-100 cursor-pointer transition-all duration-300 ease-in-out'
            size={20}
            onClick={() => setIsPanelOpen(!isPanelOpen)}
          />
        </Popover>
        : 
        <Popover
          placement='right'
          color='#1A237E'
          mouseEnterDelay={1}
          mouseLeaveDelay={0}
          content={
            <div className='text-xs font-bold text-center text-white'>
              打开二级菜单
            </div>
          }
          destroyOnHidden
        >
          <BsChevronBarRight
            className='opacity-30 hover:opacity-100 ml-1 cursor-pointer transition-all duration-300 ease-in-out'
            size={20}
            onClick={() => setIsPanelOpen(!isPanelOpen)}
          /> 
        </Popover>
      }
    </div>
    </>
  )
}

export default StuBoardPanel;
