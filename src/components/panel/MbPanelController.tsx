"use client"
import React from 'react';
import { usePathname } from 'next/navigation';
import MbStuBoardPanel from './student/board/MbStuBoardPanel';

/**
 * 移动端顶部Panel组件
 * 
 * <AUTHOR>
 * @date 2025/07/07
 * @returns 移动端顶部Panel
 */
const MbPanelController: React.FC = () => {

  const pathname: string = usePathname();

  return (
    <div className='block mt-2 min-h-11/12 w-11/12 select-none'>
      {  pathname.startsWith('/board') && <MbStuBoardPanel /> }
    </div>
  )
}

export default MbPanelController
