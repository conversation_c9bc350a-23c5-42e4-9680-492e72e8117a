import React from 'react';
import { ConfigProvider } from 'antd';
import { MessageProvider } from '@/components/context/messageContext';

interface MyConfigProviderProps {
  children: React.ReactNode;
}

const MyConfigProvider: React.FC<MyConfigProviderProps> = ({ children }) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Segmented: {
            // 整体背景
            trackBg: '#f8fafc', trackPadding: 2,
            // 未选中项样式
            itemColor: '#64748b', itemHoverColor: '#475569', itemHoverBg: '#f1f5f9', itemActiveBg: '#e2e8f0',
            // 选中项样式
            itemSelectedColor: '#1e40af', itemSelectedBg: '#dbeafe',
            // 边框和圆角
            borderRadius: 8, borderRadiusSM: 6, borderRadiusLG: 10,
            // 字体权重
            fontWeightStrong: 500,
          },
          Select: {
            // 选择器背景和边框
            colorBgContainer: '#f8fafc',
            colorBorder: '#e2e8f0',
            // 文字颜色
            colorText: '#475569',
            colorTextPlaceholder: '#94a3b8',
            // 选中项样式
            colorPrimary: '#1e40af',
            colorPrimaryHover: '#1d4ed8',
            // 下拉面板
            colorBgElevated: '#ffffff',
            // 圆角
            borderRadius: 6,
            // 选项悬停
            controlItemBgHover: '#f1f5f9',
            controlItemBgActive: '#dbeafe',
            controlItemBgActiveHover: '#bfdbfe'
          },
          Message: {
            // 消息组件样式配置
            colorBgElevated: '#ffffff',
            colorText: '#1f2937',
            colorSuccess: '#10b981',
            colorError: '#ef4444',
            colorWarning: '#f59e0b',
            colorInfo: '#3b82f6',
            borderRadius: 8,
            fontSize: 14,
            fontSizeLG: 16,
            // 消息容器样式
            paddingContentHorizontal: 16,
            paddingContentVertical: 12,
            // 阴影效果
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          }
        }
      }}
    >
      <MessageProvider>
        { children }
      </MessageProvider>
    </ConfigProvider>
  )
}

export default MyConfigProvider
