import React from 'react'

interface InfoCardProps {
  title: string;
  value: string;
  unit: string;
  icon: React.ReactNode;
}

const InfoCard: React.FC<InfoCardProps> = ({ title, value, unit, icon }) => {
  return (
    <div className='col-span-1 m-2 h-full rounded-br-xl rounded-bl-xl bg-white shadow-sm shadow-slate-200/50 hover:shadow-md hover:shadow-indigo-500/10 transition-all duration-300 ease-out hover:scale-[1.02] active:scale-[0.98] border border-slate-200/60 hover:border-indigo-200/40 select-none cursor-pointer'>
      <div className='flex flex-col items-center justify-center h-full p-6 relative overflow-hidden'>
        {/* 顶部装饰条 */}
        <div className='absolute top-0 w-full left-0 h-1 bg-gradient-to-r from-indigo-500 via-blue-500 to-indigo-600'></div>

        {/* 背景装饰元素 */}
        <div className='absolute top-2 right-2 w-16 h-16 bg-gradient-to-br from-indigo-50 to-blue-50 rounded-full opacity-60'></div>
        <div className='absolute bottom-2 left-2 w-12 h-12 bg-gradient-to-tr from-slate-50 to-indigo-50 rounded-full opacity-40'></div>

        {/* 主要内容 */}
        <div className='relative z-10 text-center'>
          <div className='mb-4 flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-indigo-500 to-blue-600 shadow-lg shadow-indigo-500/25 mx-auto'>
            {icon}
          </div>
          <div className='text-2xl font-bold text-slate-800 mb-2 tracking-tight'>{value}</div>
          <div className='text-sm text-slate-600 font-medium mb-1'>{title}</div>
          <div className='text-xs text-slate-500 font-normal'>{unit}</div>
        </div>

        {/* 数据趋势指示器 */}
        <div className='absolute bottom-3 right-3 w-2 h-2 bg-emerald-400 rounded-full shadow-sm'></div>
      </div>
    </div>
  )
}

export default InfoCard
