@import 'tailwindcss';
@import '@fontsource-variable/noto-serif-sc';

/* 移动端点击动画 */
.mb-click {
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
  &:active{
    transform: scale(1.2);
  }
}

.mb-click::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  border: 2px solid rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
  pointer-events: none;
}

.mb-click:active::after {
  animation: click-ripple 300ms ease-out;
}

@keyframes click-ripple {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  70% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.5;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

/* 自定义滚动条样式 - 契合主色调 */
.custom-scrollbar {
  /* 滚动条整体样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(99, 102, 241, 0.6) rgba(248, 250, 252, 0.3);
}

.custom-scrollbar::-webkit-scrollbar {
  width: 16px;
  height: 16px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(248, 250, 252, 0.3);
  border-radius: 10px;
  margin: 4px 0;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.7) 0%,
    rgba(79, 70, 229, 0.8) 50%,
    rgba(26, 35, 126, 0.9) 100%
  );
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.9) 0%,
    rgba(79, 70, 229, 1) 50%,
    rgba(26, 35, 126, 1) 100%
  );
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
  transform: scale(1.05);
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg,
    rgba(79, 70, 229, 1) 0%,
    rgba(26, 35, 126, 1) 50%,
    rgba(20, 28, 100, 1) 100%
  );
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.4);
  transform: scale(0.95);
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: rgba(248, 250, 252, 0.3);
  border-radius: 10px;
}

/* 移动端隐藏滚动条 */
@media (max-width: 640px) {
  .custom-scrollbar::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }

  .custom-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding-right: 0;
    margin-right: 0;
  }
}
