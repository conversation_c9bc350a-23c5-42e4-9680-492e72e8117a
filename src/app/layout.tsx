/*
 * 根布局组件，提供应用的基础HTML结构和全局样式
 *
 * @module RootLayout
 * @Author: Cicada
 * @Date: 2025/07/03
 * @param props - 组件属性
 * @param props.children - React节点，表示子组件内容
 * @returns React节点，表示根布局组件
 */
import type { Metadata, Viewport } from 'next'
import { <PERSON>ei<PERSON>, Mont<PERSON><PERSON> } from 'next/font/google'
import './globals.css'
import '@ant-design/v5-patch-for-react-19';
import Image from 'next/image'
import PanelController from '@/components/panel/PanelController'
import { SiderBarInfoProvider } from '@/components/context/SiderBarInfoContext'
import MbBottomBar from '@/components/siderbar/MbBottomBar'
import MbPanelController from '@/components/panel/MbPanelController'
import dynamic from 'next/dynamic'
import { Skeleton } from 'antd'
import { RootStyleRegistry } from '@/utils/RootStyleRegistry'
import Footer from '@/components/footer/Footer';
import MyConfigProvider from '@/components/configprovider/MyConfigProvider';

const SiderBar = dynamic(() => import('@/components/siderbar/SiderBar'), { ssr: true, loading: () => <Skeleton /> });

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const montserrat = Montserrat({
    variable: "--font-montserrat",
    subsets: ["latin"],
});

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false
}

export const metadata: Metadata = {
  title: "BUPT智慧后勤",
  icons: { icon: '/blue-icon-ex.png'},
  description: "北京邮电大学智慧后勤管理与育人平台"
};

interface ChildrenProps {
  children: React.ReactNode,
};

/**
 * 根布局组件
 * 该组件是应用的根布局，用于渲染侧边栏和主要内容区域
 * @param {Readonly<{ children: React.ReactNode }>} props - 组件属性
 * @param {React.ReactNode} props.children - React节点，表示子组件内容
 * @returns React节点，表示根布局组件
 */
const RootLayout: React.FC<ChildrenProps> = ({ children }: Readonly<{ children: React.ReactNode }>) => {

  return (
    <html lang="ch">
      <RootStyleRegistry>
        <MyConfigProvider>
        <body
          className={`${geistSans.variable} ${montserrat.variable} antialiased overflow-hidden h-screen justify-between
          bg-gradient-to-br max-sm:bg-gradient-to-tr from-[#1A237E] to-[#536DFE] text-white flex items-center w-screen
          max-sm:flex-col-reverse`}
        >
          {/* 桌面端侧边栏 */}
          <SiderBarInfoProvider>
            <div
              className="flex flex-col w-20 items-center justify-around h-full z-10 relative min-w-19
              max-sm:hidden"
            >
              <div className="flex flex-col items-center justify-center mt-6 scale-90 pointer-events-none select-none">
                <Image src="/white-icon-ex.png" alt="logo" width={60} height={60} priority className='w-auto h-auto'/>
                <span className="text-sm mt-2 text-nowrap">智慧后勤</span>
              </div>
              <SiderBar />
            </div>
          {/* 移动端底部导航栏 */}
            <div className="hidden max-sm:flex w-full h-16 z-20 min-h-16">
              {/* 作为第二方案 也可以使用 <SiderBar /> 组件 */}
              {/* <SiderBar /> */}
              <MbBottomBar />
            </div>
            </SiderBarInfoProvider>
          {/* 主体内容区域 */}
          <div
            className="bg-white flex w-full h-45/46 rounded-xl mr-2 text-neutral-950 shadow-md overflow-hidden
            max-sm:rounded-xl max-sm:flex-1 max-sm:m-2 max-sm:mb-0 max-sm:w-24/25 max-sm:max-h-[82vh]"
          >
            {/* 桌面端Panel */}
            <div className="flex h-full w-full">
                <SiderBarInfoProvider>
                  <PanelController />
                </SiderBarInfoProvider>
            {/* 主体 */}
                <div className="sm:pt-8 sm:pb-2 bg-gray-50 flex-1 h-full rounded-xl overflow-y-auto flex min-w-0 flex-col
                    max-sm:rounded-xl max-sm:pb-4 max-sm:pt-4 max-sm:items-center max-sm:justify-center">
                  <div className="w-full h-full overflow-y-auto max-w-full max-sm:flex max-sm:items-center max-sm:justify-center mb-2">
                    {children}
                  </div>
                  <Footer />
                </div>
              </div>
          </div>
          {/* 移动端顶部Panel */}
          <SiderBarInfoProvider>
            <div className="hidden max-sm:flex w-full h-12 z-20 max-sm:min-h-12 items-center justify-center">
              <MbPanelController />
            </div>
          </SiderBarInfoProvider>        
        </body>
        </MyConfigProvider>
      </RootStyleRegistry>
    </html>
  )
}

export default RootLayout;
