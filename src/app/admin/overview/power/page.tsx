"use client"
import { Segmented, Select, Spin } from 'antd';
import React, { useRef, useState } from 'react'
import { useZoom } from '@/hooks/useZoom';
import { FaMinus, FaPlus } from 'react-icons/fa';
import { RiRefreshLine } from 'react-icons/ri';
import { BsLightningChargeFill, BsDatabase, BsExclamationTriangleFill } from 'react-icons/bs';
import { HiCurrencyYen } from 'react-icons/hi';
import { TreeMapRef } from '@/components/charts/TreeMap';
import dynamic from 'next/dynamic';
import InfoCard from '@/components/infocard/InfoCard';
import MyConfigProvider from '@/components/configprovider/MyConfigProvider';
import useTotalPowerInfoNums from '@/api/admin/overview/hooks/overview/power/useTotalPowerInfoNums';
import usePowerHeatMap from '@/api/admin/overview/hooks/overview/power/usePowerHeatMap';

const HeatMap = dynamic(() => import('@/components/charts/HeatMap'), { ssr: false , loading: () => <Spin size='large'/>});
const TreeMap = dynamic(() => import('@/components/charts/TreeMap'), { ssr: false, loading: () => <Spin size='large'/> });
const LineChart = dynamic(() => import('@/components/charts/LineChart'), { ssr: false, loading: () => <Spin size='large'/> });

/* const positions: string[] = [
  '主配电室', '东配电室', '海淀校区热力中心', '小西天商业网点', '小西天锅炉房', '小西天校区', '沙河校区', '宏福校区', '交大', '东路', '二里庄'
];

const months: string[] = [
  '一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'
];

const data: (string | number) [][] = [
  // 主配电室
  [0, 0, 3135867], [1, 0, 2296987], [2, 0, 2975400], [3, 0, 2687196], [4, 0, 3474430], [5, 0, 4443089],
  [6, 0, 4272300], [7, 0, 3848818], [8, 0, 3499139], [9, 0, 2866682], [10, 0, 3179958], [11, 0, 3581140],
  // 东配电室
  [0, 1, 416646], [1, 1, 279050], [2, 1, 565650], [3, 1, 377100], [4, 1, 494843], [5, 1, 653547],
  [6, 1, 627883], [7, 1, 548352], [8, 1, 467055], [9, 1, 359848], [10, 1, 356267], [11, 1, 379730],
  // 海淀校区热力中心
  [0, 2, 155805], [1, 2, 133725], [2, 2, 78360], [3, 2, 17190], [4, 2, 19740], [5, 2, 25620],
  [6, 2, 25950], [7, 2, 27150], [8, 2, 20595], [9, 2, 21285], [10, 2, 125070], [11, 2, 216225],
  // 小西天商业网点
  [0, 3, 1154], [1, 3, 1022], [2, 3, 751], [3, 3, 562], [4, 3, 602], [5, 3, 661],
  [6, 3, 603], [7, 3, 596], [8, 3, 712], [9, 3, 578], [10, 3, 811], [11, 3, 948],
  // 小西天锅炉房
  [0, 4, 56121], [1, 4, 51816], [2, 4, 35796], [3, 4, 396], [4, 4, 445], [5, 4, 668],
  [6, 4, 1050], [7, 4, 1121], [8, 4, 676], [9, 4, 815], [10, 4, 43842], [11, 4, 55848],
  // 小西天校区
  [0, 5, 32727], [1, 5, 23768], [2, 5, 30100], [3, 5, 25685], [4, 5, 23852], [5, 5, 32123],
  [6, 5, 36391], [7, 5, 33634], [8, 5, 27064], [9, 5, 30236], [10, 5, 30804], [11, 5, 32167],
  // 沙河校区
  [0, 6, 2229550], [1, 6, 1618800], [2, 6, 2180820], [3, 6, 1744380], [4, 6, 2334720], [5, 6, 3101480],
  [6, 6, 2801790], [7, 6, 2551840], [8, 6, 2461670], [9, 6, 1958020], [10, 6, 2450030], [11, 6, 2891440],
  // 宏福校区
  [0, 7, 371750], [1, 7, 280510], [2, 7, 258250], [3, 7, 201500], [4, 7, 271610], [5, 7, 288530], [6, 7, 220070],
  // 交大
  [0, 8, 23314], [1, 8, 21185], [2, 8, 12757], [3, 8, 3451], [4, 8, 3928], [5, 8, 6288],
  [6, 8, 7327], [7, 8, 6649], [8, 8, 4444], [9, 8, 7505], [10, 8, 22409], [11, 8, 24791],
  // 东路
  [0, 9, 39348], [1, 9, 31872], [2, 9, 36685], [3, 9, 16608], [4, 9, 16149], [5, 9, 24863],
  [6, 9, 31157], [7, 9, 26299], [8, 9, 15583], [9, 9, 18154], [10, 9, 18614], [11, 9, 18835],
  // 二里庄
  [0, 10, 10624], [1, 10, 8958], [2, 10, 8195], [3, 10, 5056], [4, 10, 5405], [5, 10, 5515],
  [6, 10, 6615], [7, 10, 5804], [8, 10, 4541], [9, 10, 4602], [10, 10, 5628], [11, 10, 6363],
].map((item) => {
  return [item[0], item[1], item[2] || '-'];
}); */

const selectYearsOptions = [
  { value: '2025', label: '2025年' },
  { value: '2024', label: '2024年' },
  { value: '2023', label: '2023年' },
  { value: '2022', label: '2022年' },
  { value: '2021', label: '2021年' },
  { value: '2020', label: '2020年' },
  { value: '2019', label: '2019年' }
]

const treeData = {
  name: '累计用电',
  value: 6472906,
  children: [
    {
      name: '西土城校区',
      value: 3709472,
      children: [
        { name: '主配电室', value: 3135867 },
        { name: '东配电室', value: 416646 },
        { name: '小西天商业网点', value: 1154 },
        { name: '海淀校区热力中心', value: 155805 },
      ]
    },
    {
      name: '小西天校区',
      value: 88848,
      children: [
        { name: '南校区小西天锅炉房', value: 56121 },
        { name: '新街口外大街28号', value: 32727 },
      ]
    },
    {
      name: '二里庄和交大东路21号院',
      value: 62662,
      children: [
        { name: '一般工业用量', value: 23314 },
        { name: '居民生活用量', value: 39348 },
      ]
    },
    {
      name: '沙河校区',
      value: 2229550,
      children: [
        { name: '缴费号1020663233（临时）用电量', value: 32190 },
        { name: '缴费号1001935881（临时）用电量', value: 1130 },
        { name: '缴费号1021630258（正式）用电量', value: 2192110 },
        { name: '缴费号1037636314（临电）用电量', value: 4120 },
      ]
    },
  ]
}
  

const page: React.FC = () => {
  /* 树形图引用 */
  const treeMapRef = useRef<TreeMapRef>(null);
  /* 树形图缩放 */
  const { zoomLevel, handleZoomIn, handleZoomOut, handleResetZoom } = useZoom();
  /* 重置树形图 */
  const handleTreeMapReset = () => {
    handleResetZoom(); 
    treeMapRef.current?.resetChart(); 
  };

  /* 电力总信息数 Hook */
  const [ infoSettingYear, setInfoSettingYear ] = useState<string>('2025');
  const { total, totalCost, totalConsumption } = useTotalPowerInfoNums({ year: infoSettingYear });

  /* 热力图数据 Hook */
  const [ heatMapSettingYear, setHeatMapSettingYear ] = useState<string>('2025');
  const { positions, months, data } = usePowerHeatMap({ year: heatMapSettingYear });

  console.log(data)

  return (
    <div className='sm:pl-4 sm:pr-4 sm:pb-2 max-sm:w-24/25 h-full w-full flex flex-col gap-4 overflow-x-hidden overflow-y-auto custom-scrollbar'>
      {/* 用电信息表 */}
      <div className='bg-white w-full min-h-80 rounded-xl shadow-md inset-shadow-md shadow-indigo-500/10 p-4
        max-lg:min-h-130
        max-sm:min-h-260
      '>
        <div className='flex border-b border-neutral-400/20 pb-2 items-center justify-between select-none'>
          <span className='text-nowrap font-bold italic' style={{fontFamily: 'Noto Serif SC Variable, sans-serif'}}>用电信息表</span>
          <div className='flex gap-4 max-sm:gap-0.5 items-center justify-center'>
            <Select value={infoSettingYear} onChange={(value) => setInfoSettingYear(value)} options={selectYearsOptions} size='small' style={{width: 90}}/>
          </div>
        </div>
        <div className='h-65 w-full p-4 grid grid-cols-4 items-center gap-4
          max-lg:grid-cols-2
          max-sm:grid-cols-1 max-sm:mt-3 max-sm:gap-y-8
        '>
          <InfoCard title='数据条目数' value={total.toString()} unit='条' icon={<BsDatabase size={24} className='text-white'/>} />
          <InfoCard title='累计用电量' value={totalConsumption.toString()} unit='千瓦时' icon={<BsLightningChargeFill size={24} className='text-white'/>} />
          <InfoCard title='异常用电数' value='0' unit='条' icon={<BsExclamationTriangleFill size={24} className='text-white'/>} />
          <InfoCard title='累计用电开销' value={`¥${totalCost.toFixed(2)}`} unit='元' icon={<HiCurrencyYen size={24} className='text-white'/>} />
        </div>
      </div> 
      {/* 用电热力图  */}
      <div className='bg-white w-full min-h-80 rounded-xl shadow-md inset-shadow-md shadow-indigo-500/10 p-4'>
        <div className='flex border-b border-neutral-400/20 pb-2 items-center justify-between select-none'>
          <span className='text-nowrap font-bold italic' style={{fontFamily: 'Noto Serif SC Variable, sans-serif'}}>用电热力图</span>
          <div>
            <div className='flex gap-4 max-sm:gap-0.5 items-center justify-center'>
              <Select value={heatMapSettingYear} onChange={(value) => setHeatMapSettingYear(value)} options={selectYearsOptions} size='small' style={{width: 90}}/>
              <Segmented options={['用电量', '金额']} shape='round' size='small'/>
            </div>
          </div>
        </div>
        <div className='h-full w-full grid items-center'>
          {/* 热力图组件 */}
          <HeatMap xData={months} yData={positions} data={data}/>
        </div>
      </div>
      {/* 用电树形图 + 用电趋势图 */}
      <div className='h-full w-full grid grid-cols-12 gap-4
        max-lg:flex max-lg:flex-col
        max-sm:flex max-sm:flex-col
      '>
        {/* 用电树形图 */}
        <div className='bg-white w-full h-80 rounded-xl shadow-md inset-shadow-md shadow-indigo-500/10 p-4 col-span-7'>
          <div className='flex border-b border-neutral-400/20 pb-2 items-center justify-between select-none'>
            <span className='text-nowrap font-bold italic' style={{fontFamily: 'Noto Serif SC Variable, sans-serif'}}>用电树形图</span>
              <div className="grid grid-cols-32 items-center justify-center w-40 text-sm text-[#333] gap-2 mr-4">
                <div className='col-span-12'>
                  <Select defaultValue={'2025'} options={[{ value: '2025', label: '2025年' }, { value: '2024', label: '2024年' }, { value: '2023', label: '2023年' }]} size='small' style={{width: 90}}/>
                </div>
                <button onClick={handleZoomIn} className='col-span-4 flex items-center justify-center cursor-pointer mb-click'><FaPlus size={15}/></button>
                <button onClick={handleZoomOut} className='col-span-4 flex items-center justify-center cursor-pointer mb-click'><FaMinus size={15}/></button>
                <button onClick={handleTreeMapReset} className='col-span-4 flex items-center justify-center cursor-pointer mb-click'><RiRefreshLine size={20}/></button>
                <div className='col-span-4'>{`${zoomLevel.toFixed(2)}x`}</div>
              </div>
          </div>
          <div className='h-full w-full grid items-center'>
            {/* 树形图组件 */}
            <TreeMap
              ref={treeMapRef}
              data={treeData}
              zoomLevel={zoomLevel}
              onZoomIn={handleZoomIn}
              onZoomOut={handleZoomOut}
              onResetZoom={handleTreeMapReset}
              enableZoomControl
            />
          </div>
        </div>
        {/* 用电趋势图 */}
        <div className='bg-white w-full h-80 rounded-xl shadow-md inset-shadow-md shadow-indigo-500/10 p-4 col-span-5'>
          <div className='flex border-b border-neutral-400/20 pb-2 items-center justify-between select-none'>
            <span className='text-nowrap font-bold italic' style={{fontFamily: 'Noto Serif SC Variable, sans-serif'}}>用电趋势图</span>
            <div>
              <MyConfigProvider>
                <div className='flex gap-4 max-sm:gap-0.5 items-center justify-center'>
                  <Select defaultValue={'2024'} options={[{ value: '2024', label: '2024年' }, { value: '2023', label: '2023年' }]} size='small' style={{width: 90}}/>
                  <Select defaultValue={'主配电室'} options={[{ value: '主配电室', label: '主配电室' }, { value: '小西天', label: '小西天' }]} size='small' style={{width: 120}}/>
                </div>
              </MyConfigProvider>
            </div>
          </div>
          <div className='h-full w-full grid items-center'>
            {/* 折线图组件 */}
            <LineChart 
              data={[6472906, 4747693, 6182764, 5079124, 6645724, 8582384, 8031136, 7050263, 6501479, 5267725, 6233433, 7207487 ]} 
              xLabels={['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default page