import { useState, useCallback } from 'react';

export const useZoom = (initialZoom: number = 1, minZoom: number = 0.3, maxZoom: number = 3) => {
  const [zoomLevel, setZoomLevel] = useState(initialZoom);

  const handleZoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(prev * 1.3, maxZoom));
  }, [maxZoom]);

  const handleZoomOut = useCallback(() => {
    setZoomLevel(prev => Math.max(prev / 1.3, minZoom));
  }, [minZoom]);

  const handleResetZoom = useCallback(() => {
    setZoomLevel(initialZoom);
  }, [initialZoom]);

  return {
    zoomLevel,
    handleZoomIn,
    handleZoomOut,
    handleResetZoom
  };
};
