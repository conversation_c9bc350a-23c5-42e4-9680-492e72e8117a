"use client"
import { useEffect } from 'react';
import { useMessage, MessageUtils } from '@/components/context/messageContext';

/**
 * 消息工具类初始化Hook
 * 
 * <AUTHOR>
 * @date 2025/07/18
 * @description 自动初始化MessageUtils的Hook，简化使用流程
 * @returns void
 */
export const useMessageUtils = (): void => {
  const messageApi = useMessage();

  useEffect(() => {
    MessageUtils.setMessageApi(messageApi);
  }, [messageApi]);
};

/**
 * 带返回值的消息工具类Hook
 * 
 * <AUTHOR>
 * @date 2025/07/18
 * @description 初始化MessageUtils并返回消息API实例
 * @returns 消息API实例和工具类方法
 */
export const useMessageWithUtils = () => {
  const messageApi = useMessage();

  useEffect(() => {
    MessageUtils.setMessageApi(messageApi);
  }, [messageApi]);

  return {
    messageApi,
    MessageUtils
  };
};
