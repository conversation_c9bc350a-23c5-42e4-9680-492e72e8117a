import axios, { AxiosError, InternalAxiosRequestConfig } from "axios";
import { MessageUtils } from "@/components/context/messageContext";

/**
 * axios请求封装
 *
 * <AUTHOR>
 * @date 2025/07/18
 * @description axios请求封装，集成统一消息提示
 * @returns axios实例
 */
const service = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
  timeout: 5000,
})

/**
 * 请求拦截器
 *
 * <AUTHOR>
 * @date 2025/07/18
 * @description 请求拦截器，处理请求错误
 * @returns axios实例
 */
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    return config;
  },
  (error: AxiosError) => {
    MessageUtils.destroy();
    MessageUtils.error('请求失败，请检查后重试', 5, 'request_1');
    return Promise.reject(error);
  }
)

/**
 * 响应拦截器
 *
 * <AUTHOR>
 * @date 2025/07/18
 * @description 响应拦截器，处理响应错误和状态码
 * @returns axios实例
 */
service.interceptors.response.use(
  (response) => {
    const { status } = response;
    if (status === 200) {
      return response;
    }
    else {
      MessageUtils.destroy();
      MessageUtils.error('请求结果有误，请检查后重试', 5, 'response_1');
      return Promise.reject(response);
    }
  },
  (error: AxiosError) => {
    const { response } = error;
    MessageUtils.destroy();

    if(JSON.stringify(error).includes("Network Error")) {
      MessageUtils.error('网络连接失败，请检查网络后重试', 5, 'response_2');
    }

    if(response?.status === 401) {
      MessageUtils.error('登录已过期，请重新登录', 5, 'response_3');
    }

    if(response?.status === 403) {
      MessageUtils.error('权限不足，请联系管理员', 5, 'response_4');
    }

    if(response?.status === 500) {
      MessageUtils.error('服务器错误，请联系管理员', 5, 'response_5');
    }

    if(response?.status === 400) {
      MessageUtils.error('请求错误，请检查后重试', 5, 'response_6');
    }

    if(response?.status === 404) {
      MessageUtils.error('请求资源不存在，请检查后重试', 5, 'response_7');
    }

    if(response?.status === 405) {
      MessageUtils.error('请求方法不被允许，请检查后重试', 5, 'response_8');
    }

    return Promise.reject(error);
  }
)

export default service;