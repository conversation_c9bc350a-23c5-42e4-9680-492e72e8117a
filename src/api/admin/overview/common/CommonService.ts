import http from '@/utils/AxiosIndex';

/**
 * 电力数据类型定义
 */
export interface PowerData {
  id: number;
  year: number;
  month: number;
  region_name: string;
  consumption_kwh: string;
  cost_rmb: string;
}

/**
 * 电力区域数据类型定义
 */
export interface PowerRegionData {
  region_name: string;
  total_consumption_kwh: number;
}

/**
 * 电力TopN数据类型定义
 */
export interface PowerTopNData {
  region_name: string;
  consumption_kwh: number;
}

/**
 * 接口地址列表
 */
const api = {
  powerList: '/api/power/',
  powerRegion: '/api/power/region/',
  powerTopN: '/api/power/top/',
  flowList: '/api/traffic/'
}

/**
 * 获取电量列表
 *
 * @param year 年份筛选（可选）
 * @param month 月份筛选（可选）
 * @param region_name 区域筛选（可选）
 * @returns 获取用电量列表
 */
export function getPowerList(year?: string, month?: string, region_name?: string) {
  const params = new URLSearchParams();

  if (year != null) params.append('year', year);
  if (month != null) params.append('month', month);
  if (region_name != null) params.append('region_name', region_name);

  const url = params.toString() ? `${api.powerList}?${params.toString()}` : api.powerList;
  return http.get<PowerData[]>(url);
}

/**
 * 获取电量区域列表
 *
 * @param year 年份筛选（可选）
 * @param month 月份筛选（可选）
 * @returns 获取用电量区域列表
 */
export function getPowerRegion(year?: string, month?: string) {
  const params = new URLSearchParams();

  if (year != null) params.append('year', year);
  if (month != null) params.append('month', month);

  const url = params.toString() ? `${api.powerRegion}?${params.toString()}` : api.powerRegion;
  return http.get<PowerRegionData[]>(url);
}

/**
 * 获取电量TOPN列表
 *
 * @param year 年份筛选（可选）
 * @param month 月份筛选（可选）
 * @param n TOPN数量（可选）
 * @returns 获取用电量TOPN列表
 */
export function getPowerTopN(year?: string, month?: string, n?: string) {
  const params = new URLSearchParams();

  if (year != null) params.append('year', year);
  if (month != null) params.append('month', month);
  if (n != null) params.append('n', n);

  const url = params.toString() ? `${api.powerTopN}?${params.toString()}` : api.powerTopN;
  return http.get<PowerTopNData[]>(url);
}

/**
 * 获取食堂流量列表
 *
 * @param location 食堂名称
 * @param start_date 开始时间 YYYY-MM-DD
 * @param end_date 结束时间 YYYY-MM-DD
 * @returns 获取食堂流量列表
 */
export function getFlowList(location: string, start_date: string, end_date: string) {
  const params = new URLSearchParams();

  params.append('location', location);
  params.append('start_date', start_date);
  params.append('end_date', end_date);

  const url = `${api.flowList}?${params.toString()}`;
  return http.get(url);
}