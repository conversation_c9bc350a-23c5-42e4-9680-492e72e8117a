'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { getPowerList, type PowerData } from '../../../common/CommonService';

/**
 * Hook 参数类型定义
 */
interface UsePowerHeatMapParams {
  year?: string;
  dataType?: 'consumption' | 'cost'; // 数据类型：用电量或金额
}

/**
 * Hook 返回值类型定义
 */
interface UsePowerHeatMapReturn {
  positions: string[];
  months: string[];
  data: (string | number)[][];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

/**
 * 月份映射
 */
const MONTHS: string[] = [
  '一月', '二月', '三月', '四月', '五月', '六月',
  '七月', '八月', '九月', '十月', '十一月', '十二月'
];

/**
 * 电力热力图数据获取和处理 Hook
 *
 * @param params - Hook 参数
 * @param params.year - 年份筛选（可选）
 * @param params.dataType - 数据类型：'consumption' 用电量 | 'cost' 金额
 * @returns Hook 返回值
 */
export const usePowerHeatMap = ({
  year,
  dataType = 'consumption'
}: UsePowerHeatMapParams = {}): UsePowerHeatMapReturn => {
  const [rawData, setRawData] = useState<PowerData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 获取数据
   */
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getPowerList(year);
      // 确保 response.data 是数组类型
      const dataArray = Array.isArray(response.data) ? response.data : [];
      setRawData(dataArray as PowerData[]);
    } catch (err) {
      console.error('获取电力数据失败:', err);
      setError('获取数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [year]);

  /**
   * 初始化和年份变化时获取数据
   */
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  /**
   * 处理数据：提取所有唯一的区域名称
   */
  const positions = useMemo(() => {
    const uniqueRegions = Array.from(
      new Set(rawData.map(item => item.region_name))
    );
    return uniqueRegions.sort(); // 按字母顺序排序
  }, [rawData]);

  /**
   * 处理数据：转换为热力图格式
   */
  const data = useMemo(() => {
    if (!rawData.length || !positions.length) {
      return [
        [0, 0, 0]
      ];
    }

    const heatMapData: (string | number)[][] = [];

    // 遍历每个月份和每个区域
    MONTHS.forEach((_, monthIndex) => {
      positions.forEach((region, regionIndex) => {
        // 查找对应的数据
        const dataItem = rawData.find(
          item => item.month === monthIndex + 1 && item.region_name === region
        );

        if (dataItem) {
          // 根据数据类型选择对应的值
          const value = dataType === 'consumption'
            ? parseFloat(dataItem.consumption_kwh)
            : parseFloat(dataItem.cost_rmb);

          heatMapData.push([monthIndex, regionIndex, value || 0]);
        } else {
          // 没有数据时使用 '-' 占位
          heatMapData.push([monthIndex, regionIndex, '-']);
        }
      });
    });

    return heatMapData;
  }, [rawData, positions, dataType]);

  return {
    positions,
    months: MONTHS,
    data,
    loading,
    error,
    refetch: fetchData
  };
};

/**
 * 默认导出 Hook
 */
export default usePowerHeatMap;