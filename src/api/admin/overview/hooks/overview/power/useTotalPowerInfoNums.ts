'use client';

import { useState, useEffect, useCallback } from 'react';
import { getPowerList } from '../../../common/CommonService';

/**
 * Hook 参数类型定义
 */
interface UseTotalPowerInfoNumsParams {
  year?: string;
}

/**
 * Hook 返回值类型定义
 */
interface UseTotalPowerInfoNumsReturn {
  total: number;
  totalConsumption: number;
  loading: boolean;
  error: string | null;
  refetch: () => void;
  totalCost: number;
}

/**
 * 电力总信息数 Hook
 *
 * @param params - Hook 参数
 * @param params.year - 年份筛选（可选）
 * @returns Hook 返回值
 */
export const useTotalPowerInfoNums = ({
  year,
}: UseTotalPowerInfoNumsParams = {}): UseTotalPowerInfoNumsReturn => {
  const [total, setTotal] = useState<number>(0);
  const [totalConsumption, setTotalConsumption] = useState<number>(0);
  const [totalCost, setTotalCost] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 获取数据
   */
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getPowerList(year);
      // 确保 response.data 是数组类型
      const dataArray = Array.isArray(response.data) ? response.data : [];
      // 计算总条目数
      const total = dataArray.length;
      // 计算总用电量
      const totalConsumption = dataArray.reduce((acc, item) => {
        const consumption = parseFloat(item.consumption_kwh);
        return acc + (isNaN(consumption) ? 0 : consumption);
      }, 0);
      // 计算总金额
      const totalCost = dataArray.reduce((acc, item) => {
        const cost = parseFloat(item.cost_rmb);
        return acc + (isNaN(cost) ? 0 : cost);
      }, 0);

      setTotal(total);
      setTotalConsumption(totalConsumption);
      setTotalCost(totalCost);
    } catch (err) {
      console.error('获取电力数据失败:', err);
      setError('获取数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [year]);

  /**
   * 初始化和年份变化时获取数据
   */
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    total,
    totalConsumption,
    totalCost,
    loading,
    error,
    refetch: fetchData
  };
};

/**
 * 默认导出 Hook
 */
export default useTotalPowerInfoNums;
